name: Build and Release

on:
  push:
    tags:
      - 'v*'  # 触发条件：推送 v* 标签时（如 v1.0.0）
  workflow_dispatch:  # 允许手动触发

permissions:
  contents: write

jobs:
  build-windows:
    runs-on: windows-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.32.4'  # 指定 Flutter 版本，可根据需要调整
        channel: 'stable'
        
    - name: Install dependencies
      run: flutter pub get
      
    - name: Build Windows EXE
      run: flutter build windows --release
      
    - name: Create Windows archive
      run: |
        cd build\windows\x64\runner\Release
        7z a -tzip windows-release.zip *
        
    - name: Upload Windows artifact
      uses: actions/upload-artifact@v4
      with:
        name: windows-release
        path: build/windows/x64/runner/Release/windows-release.zip

  build-android:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.32.4'  # 指定 Flutter 版本，可根据需要调整
        channel: 'stable'
        
    - name: Install dependencies
      run: flutter pub get
      
    - name: Update dependencies
      run: flutter pub upgrade --major-versions
      
    - name: Build Android APK (split per ABI)
      run: flutter build apk --release --split-per-abi
      
    - name: Upload Android artifacts
      uses: actions/upload-artifact@v4
      with:
        name: android-release
        path: build/app/outputs/flutter-apk/*.apk

  release:
    needs: [build-windows, build-android]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download Windows artifact
      uses: actions/download-artifact@v4
      with:
        name: windows-release
        path: ./artifacts/windows
        
    - name: Download Android artifacts
      uses: actions/download-artifact@v4
      with:
        name: android-release
        path: ./artifacts/android
        
    - name: Get tag name
      id: tag
      run: echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
      
    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ steps.tag.outputs.tag }}
        name: Release ${{ steps.tag.outputs.tag }}
        draft: false
        prerelease: false
        generate_release_notes: true
        files: |
          ./artifacts/windows/windows-release.zip
          ./artifacts/android/*.apk
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
