{"buildFiles": ["D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\github\\to_do_list\\android\\app\\.cxx\\RelWithDebInfo\\q2k3a2m2\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\github\\to_do_list\\android\\app\\.cxx\\RelWithDebInfo\\q2k3a2m2\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}