{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Android/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Android/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Android/cmake/3.22.1/bin/ctest.exe", "root": "D:/Android/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-0d119aa06a6a2a0c99a4.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-cab67f42f53e63a04c4c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-4fecc4f3f5ce69b76d80.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-cab67f42f53e63a04c4c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-4fecc4f3f5ce69b76d80.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-0d119aa06a6a2a0c99a4.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}