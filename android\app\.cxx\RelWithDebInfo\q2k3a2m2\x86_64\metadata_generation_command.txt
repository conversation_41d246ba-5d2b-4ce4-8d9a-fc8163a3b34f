                        -HD:\Flutter\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=D:\Android\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\Android\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\Android\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\github\to_do_list\build\app\intermediates\cxx\RelWithDebInfo\q2k3a2m2\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\github\to_do_list\build\app\intermediates\cxx\RelWithDebInfo\q2k3a2m2\obj\x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\github\to_do_list\android\app\.cxx\RelWithDebInfo\q2k3a2m2\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2