{"buildFiles": ["D:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\github\\to_do_list\\android\\app\\.cxx\\RelWithDebInfo\\q2k3a2m2\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\github\\to_do_list\\android\\app\\.cxx\\RelWithDebInfo\\q2k3a2m2\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Android\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Android\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}